import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TextField,
  IconButton,
  Box,
  Typography,
  Alert,
  CircularProgress,
  Tooltip,
  Paper,
  TableContainer,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import {
  loadCostCenterMappings,
  saveCostCenterMappings,
  deleteCostCenterMapping,
} from '../db';

const CostCenterMappingModal = ({ open, onClose, costCenters = [] }) => {
  const [mappings, setMappings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Load existing mappings when modal opens
  useEffect(() => {
    if (open) {
      loadMappings();
    }
  }, [open]);

  const loadMappings = async () => {
    setLoading(true);
    setError('');
    try {
      const existingMappings = await loadCostCenterMappings();
      
      // Create a mapping object for quick lookup
      const mappingLookup = {};
      existingMappings.forEach(mapping => {
        mappingLookup[mapping.originalName] = mapping;
      });

      // Create mappings array with all cost centers
      const allMappings = costCenters.map(costCenter => {
        const existing = mappingLookup[costCenter];
        return {
          id: existing?.id || null,
          originalName: costCenter,
          alternateName: existing?.alternateName || '',
          isNew: !existing,
          isModified: false,
        };
      });

      setMappings(allMappings);
    } catch (err) {
      setError('Failed to load cost center mappings');
      console.error('Error loading mappings:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAlternateNameChange = (index, value) => {
    const newMappings = [...mappings];
    newMappings[index] = {
      ...newMappings[index],
      alternateName: value,
      isModified: true,
    };
    setMappings(newMappings);
    setError('');
    setSuccess('');
  };

  const handleAddMapping = () => {
    const newMapping = {
      id: null,
      originalName: '',
      alternateName: '',
      isNew: true,
      isModified: false,
    };
    setMappings([...mappings, newMapping]);
  };

  const handleDeleteMapping = async (index) => {
    const mapping = mappings[index];
    
    if (mapping.id) {
      // Delete from database if it exists
      try {
        await deleteCostCenterMapping(mapping.id);
        setSuccess('Mapping deleted successfully');
      } catch (err) {
        setError('Failed to delete mapping');
        console.error('Error deleting mapping:', err);
        return;
      }
    }

    // Remove from local state
    const newMappings = mappings.filter((_, i) => i !== index);
    setMappings(newMappings);
  };

  const handleSave = async () => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Filter out mappings that have alternate names and are modified or new
      const mappingsToSave = mappings
        .filter(mapping => 
          mapping.alternateName.trim() !== '' && 
          (mapping.isModified || mapping.isNew)
        )
        .map(mapping => ({
          originalName: mapping.originalName.trim(),
          alternateName: mapping.alternateName.trim(),
        }));

      if (mappingsToSave.length > 0) {
        await saveCostCenterMappings(mappingsToSave);
        setSuccess(`Successfully saved ${mappingsToSave.length} cost center mappings`);
        
        // Reload mappings to get updated data
        await loadMappings();
      } else {
        setSuccess('No changes to save');
      }
    } catch (err) {
      setError('Failed to save cost center mappings');
      console.error('Error saving mappings:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  const hasChanges = mappings.some(mapping => 
    mapping.isModified || (mapping.isNew && mapping.alternateName.trim() !== '')
  );

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        component: motion.div,
        initial: { opacity: 0, scale: 0.9 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.9 },
        transition: { duration: 0.2 },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Cost Center Mappings</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Configure alternate names for cost centers. These alternate names will be displayed in the Tally report while preserving the original data.
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Original Cost Center</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Alternate Name</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', width: 100 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <AnimatePresence>
                  {mappings.map((mapping, index) => (
                    <TableRow
                      key={`${mapping.originalName}-${index}`}
                      component={motion.tr}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      sx={{
                        backgroundColor: mapping.isModified ? 'rgba(25, 118, 210, 0.04)' : 'inherit',
                      }}
                    >
                      <TableCell>
                        {mapping.isNew ? (
                          <TextField
                            size="small"
                            value={mapping.originalName}
                            onChange={(e) => {
                              const newMappings = [...mappings];
                              newMappings[index].originalName = e.target.value;
                              setMappings(newMappings);
                            }}
                            placeholder="Enter cost center name"
                            fullWidth
                          />
                        ) : (
                          <Typography variant="body2">{mapping.originalName}</Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          value={mapping.alternateName}
                          onChange={(e) => handleAlternateNameChange(index, e.target.value)}
                          placeholder="Enter alternate name"
                          fullWidth
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Delete mapping">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteMapping(index)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </AnimatePresence>
              </TableBody>
            </Table>
          </TableContainer>
        )}

        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-start' }}>
          <Button
            startIcon={<AddIcon />}
            onClick={handleAddMapping}
            variant="outlined"
            size="small"
          >
            Add New Mapping
          </Button>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={saving}>
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={!hasChanges || saving}
          startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CostCenterMappingModal;
