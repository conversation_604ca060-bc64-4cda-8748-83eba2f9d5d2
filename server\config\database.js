const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.dbPath = process.env.DB_PATH || './database/finreport.db';
    this.initialized = false;
  }

  init() {
    return new Promise((resolve, reject) => {
      if (this.initialized) {
        resolve();
        return;
      }

      try {
        // Ensure database directory exists
        const dbDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dbDir)) {
          fs.mkdirSync(dbDir, { recursive: true });
        }

        // Initialize database connection
        this.db = new sqlite3.Database(this.dbPath, (err) => {
          if (err) {
            console.error('Database connection failed:', err);
            reject(err);
            return;
          }

          console.log(`Database connected: ${this.dbPath}`);

          // Enable foreign keys
          this.db.run('PRAGMA foreign_keys = ON', (err) => {
            if (err) {
              console.error('Failed to enable foreign keys:', err);
              reject(err);
              return;
            }

            this.createTables()
              .then(() => {
                this.initialized = true;
                resolve();
              })
              .catch(reject);
          });
        });
      } catch (error) {
        console.error('Database initialization failed:', error);
        reject(error);
      }
    });
  }

  createTables() {
    return new Promise((resolve, reject) => {
      const tables = [
        // Cost Center table
        `CREATE TABLE IF NOT EXISTS cost_center (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          costCenter TEXT NOT NULL,
          hoursWorked REAL NOT NULL,
          month TEXT NOT NULL,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
          source TEXT DEFAULT 'manual'
        )`,

        // Salary table
        `CREATE TABLE IF NOT EXISTS salary (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          payrollMonth TEXT NOT NULL,
          name TEXT NOT NULL,
          gross REAL NOT NULL,
          pf REAL DEFAULT 0,
          salaryMaster TEXT,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
          source TEXT DEFAULT 'manual'
        )`,

        // Salary Master table
        `CREATE TABLE IF NOT EXISTS salary_master (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          designation TEXT,
          department TEXT,
          baseSalary REAL,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )`,

        // Resource Utilization table (computed from cost_center)
        `CREATE TABLE IF NOT EXISTS resource_utilization (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          employee TEXT NOT NULL,
          month TEXT NOT NULL,
          costCenter TEXT NOT NULL,
          hours REAL NOT NULL,
          totalHours REAL NOT NULL,
          utilizationPercentage REAL,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )`,

        // Resource Cost table (computed from cost_center and salary)
        `CREATE TABLE IF NOT EXISTS resource_cost (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          employee TEXT NOT NULL,
          month TEXT NOT NULL,
          costCenter TEXT NOT NULL,
          hours REAL NOT NULL,
          grossCost REAL NOT NULL,
          pfCost REAL NOT NULL,
          totalCost REAL NOT NULL,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )`,

        // Employee Discrepancy table (computed differences)
        `CREATE TABLE IF NOT EXISTS employee_discrepancy (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          employee TEXT NOT NULL,
          discrepancyType TEXT NOT NULL, -- 'missing_from_cost_center' or 'missing_from_salary'
          month TEXT,
          details TEXT,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )`,

        // Pagination State table
        `CREATE TABLE IF NOT EXISTS pagination_state (
          key TEXT PRIMARY KEY,
          component TEXT NOT NULL,
          currentPage INTEGER DEFAULT 1,
          itemsPerPage INTEGER DEFAULT 100,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )`,

        // Cost Center Mappings table
        `CREATE TABLE IF NOT EXISTS cost_center_mappings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          originalName TEXT NOT NULL UNIQUE,
          alternateName TEXT NOT NULL,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )`
      ];

      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_cost_center_name_month ON cost_center(name, month)',
        'CREATE INDEX IF NOT EXISTS idx_salary_name_month ON salary(name, payrollMonth)',
        'CREATE INDEX IF NOT EXISTS idx_resource_util_employee_month ON resource_utilization(employee, month)',
        'CREATE INDEX IF NOT EXISTS idx_resource_cost_employee_month ON resource_cost(employee, month)',
        'CREATE INDEX IF NOT EXISTS idx_employee_discrepancy_employee ON employee_discrepancy(employee)',
        'CREATE INDEX IF NOT EXISTS idx_cost_center_mappings_original ON cost_center_mappings(originalName)'
      ];

      let completed = 0;
      const total = tables.length + indexes.length;

      const checkComplete = () => {
        completed++;
        if (completed === total) {
          console.log('Database tables and indexes created successfully');
          resolve();
        }
      };

      // Create tables
      tables.forEach(sql => {
        this.db.run(sql, (err) => {
          if (err) {
            console.error('Error creating table:', err);
            reject(err);
            return;
          }
          checkComplete();
        });
      });

      // Create indexes
      indexes.forEach(sql => {
        this.db.run(sql, (err) => {
          if (err) {
            console.error('Error creating index:', err);
            reject(err);
            return;
          }
          checkComplete();
        });
      });
    });
  }

  getDatabase() {
    return this.db;
  }

  close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
          } else {
            console.log('Database connection closed');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  // Backup database
  backup() {
    const backupDir = process.env.DB_BACKUP_PATH || './database/backups';
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `finreport_backup_${timestamp}.db`);

    try {
      // Simple file copy for backup with sqlite3
      fs.copyFileSync(this.dbPath, backupPath);
      console.log(`Database backed up to: ${backupPath}`);
      return backupPath;
    } catch (error) {
      console.error('Backup failed:', error);
      throw error;
    }
  }
}

const dbManager = new DatabaseManager();
module.exports = dbManager;
