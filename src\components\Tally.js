// src/components/Tally.js
import React, { useState, useEffect } from "react";
import {
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Button,
  CircularProgress,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  Card,
  CardContent,
  IconButton,
  Tooltip,
} from "@mui/material";
import { Settings as SettingsIcon } from "@mui/icons-material";
import { motion } from "framer-motion";
import * as XLSX from "xlsx";
import { loadCostCenterData, loadSalaryData, loadCostCenterMappings } from "../db";
import CostCenterMappingModal from "./CostCenterMappingModal";
import { applyCostCenterMapping, getUniqueCostCenters } from "../utils/costCenterMappings";

const Tally = ({ selectedMonth }) => {
  // Local state
  const [month, setMonth] = useState(selectedMonth || "");
  const [uniqueMonths, setUniqueMonths] = useState([]);
  const [aggregatedData, setAggregatedData] = useState([]);
  const [differenceData, setDifferenceData] = useState([]);
  const [totals, setTotals] = useState({ ccGross: 0, ccPF: 0, salaryGross: 0, salaryPF: 0 });
  const [loading, setLoading] = useState(false);

  // Cost center mapping state
  const [costCenterMappings, setCostCenterMappings] = useState({});
  const [mappingModalOpen, setMappingModalOpen] = useState(false);
  const [uniqueCostCenters, setUniqueCostCenters] = useState([]);

  // When parent passes a selectedMonth, update local state
  useEffect(() => {
    setMonth(selectedMonth);
  }, [selectedMonth]);

  // Load cost center mappings
  useEffect(() => {
    const loadMappings = async () => {
      try {
        const mappings = await loadCostCenterMappings();
        const mappingObject = {};
        mappings.forEach(mapping => {
          mappingObject[mapping.originalName] = mapping.alternateName;
        });
        setCostCenterMappings(mappingObject);
      } catch (error) {
        console.error('Error loading cost center mappings:', error);
      }
    };
    loadMappings();

    // Listen for database changes to reload mappings
    const handleDBChange = () => loadMappings();
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, []);

  // Fetch unique months from cost center data and update local state
  useEffect(() => {
    const fetchUniqueMonths = async () => {
      const ccData = await loadCostCenterData();
      const months = ccData
        .filter(record => record.month && record.month.trim() !== "")
        .map(record => record.month.trim());
      const unique = [...new Set(months)];
      setUniqueMonths(unique);

      // Extract ALL unique cost centers from the entire dataset for the mapping modal
      const allCostCenters = getUniqueCostCenters(ccData, 'costCenter');
      setUniqueCostCenters(allCostCenters);

      // Clear the selected month if it no longer exists.
      if (month && !unique.includes(month)) {
        setMonth("");
      }
      // If no month is selected and there is at least one, default to the first
      if (!month && unique.length > 0) {
        setMonth(unique[0]);
      }
    };
    fetchUniqueMonths();
  }, [month]); // Re-run whenever month changes (or deletion causes update)

  // Calculate tally data based on selected month
  useEffect(() => {
    const calculateResourceCost = async () => {
      if (!month) return;
      setLoading(true);
      try {
        const ccData = await loadCostCenterData();
        const salaryData = await loadSalaryData();

        // Filter cost center data for the selected month
        const filteredCC = ccData.filter(record => record.month && record.month.trim() === month);

        // Get unique cost centers for this month's calculation
        const costCentersSet = new Set();
        filteredCC.forEach(record => {
          if (record.costCenter) {
            costCentersSet.add(record.costCenter.trim());
          }
        });
        const costCenters = Array.from(costCentersSet);

        // Calculate totals per cost center based on each employee's hours
        const ccTotals = {};
        filteredCC.forEach(record => {
          const cc = record.costCenter ? record.costCenter.trim() : "";
          if (!cc) return;
          // Parse hours (prefer hoursWorked)
          const hrs = parseFloat(record.hoursWorked || record.hours || 0);
          if (hrs <= 0) return;
          // Get employee's all records for the month
          const empRecords = filteredCC.filter(
            r => r.name && r.name.trim().toLowerCase() === record.name.trim().toLowerCase()
          );
          const totalHours = empRecords.reduce(
            (sum, r) => sum + (parseFloat(r.hoursWorked || r.hours || 0)),
            0
          );

          // Get employee's salary record for the selected month (assumes payrollMonth field)
          const salaryRecord = salaryData.find(s =>
            s.name && s.name.trim().toLowerCase() === record.name.trim().toLowerCase() &&
            s.payrollMonth && s.payrollMonth.trim() === month
          );
          if (!salaryRecord) return;
          const salary = {
            gross: parseFloat(salaryRecord.gross || 0),
            pf: parseFloat(salaryRecord.pf || 0)
          };

          // Compute cost for this record
          const cost = {
            gross: totalHours > 0 ? (salary.gross / totalHours) * hrs : 0,
            pf: totalHours > 0 ? (salary.pf / totalHours) * hrs : 0
          };

          if (!ccTotals[cc]) {
            ccTotals[cc] = { gross: 0, pf: 0 };
          }
          ccTotals[cc].gross += cost.gross;
          ccTotals[cc].pf += cost.pf;
        });

        // Calculate overall salary totals for the month
        const salaryTotals = { gross: 0, pf: 0 };
        salaryData.forEach(record => {
          if (record.payrollMonth && record.payrollMonth.trim() === month) {
            salaryTotals.gross += parseFloat(record.gross || 0);
            salaryTotals.pf += parseFloat(record.pf || 0);
          }
        });

        // Prepare aggregated data array from costCenters and totals (without the employees column)
        const aggregated = costCenters.map(cc => ({
          costCenter: applyCostCenterMapping(cc, costCenterMappings),
          originalCostCenter: cc,
          gross: ccTotals[cc] ? ccTotals[cc].gross : 0,
          pf: ccTotals[cc] ? ccTotals[cc].pf : 0,
        }));

        // Calculate differences for each cost center
        const differences = aggregated.map(item => ({
          ...item,
          grossDiff: salaryTotals.gross - item.gross,
          pfDiff: salaryTotals.pf - item.pf,
          salaryGross: salaryTotals.gross,
          salaryPF: salaryTotals.pf
        }));

        setAggregatedData(aggregated);
        setDifferenceData(differences);
        setTotals({
          ccGross: aggregated.reduce((sum, item) => sum + item.gross, 0),
          ccPF: aggregated.reduce((sum, item) => sum + item.pf, 0),
          salaryGross: salaryTotals.gross,
          salaryPF: salaryTotals.pf
        });
      } catch (error) {
        console.error("Error calculating resource cost:", error);
        setAggregatedData([]);
        setDifferenceData([]);
        setTotals({ ccGross: 0, ccPF: 0, salaryGross: 0, salaryPF: 0 });
      } finally {
        setLoading(false);
      }
    };
    calculateResourceCost();
  }, [month]);

  const handleExport = () => {
    if (!month || aggregatedData.length === 0) return;

    const wb = XLSX.utils.book_new();

    // First worksheet - Tally Data without employees column
    const tallyData = [
      ["Sl No", "Cost Center", "Original Cost Center", "Gross Salary", "PF"],
      ...aggregatedData.map((row, idx) => [
        idx + 1,
        row.costCenter || "",
        row.originalCostCenter || row.costCenter || "",
        (row.gross || 0).toFixed(2),
        (row.pf || 0).toFixed(2),
      ]),
      [
        "Total",
        "",
        "",
        totals.ccGross.toFixed(2),
        totals.ccPF.toFixed(2),
      ],
    ];
    const ws1 = XLSX.utils.aoa_to_sheet(tallyData);
    XLSX.utils.book_append_sheet(wb, ws1, "Tally Data");

    // Second worksheet - Differences
    const differencesData = [
      [
        "Sl No",
        "Cost Center",
        "Original Cost Center",
        "Cost Center Gross",
        "Salary Gross",
        "Gross Difference",
        "Cost Center PF",
        "Salary PF",
        "PF Difference",
      ],
      ...differenceData.map((row, idx) => [
        idx + 1,
        row.costCenter || "",
        row.originalCostCenter || row.costCenter || "",
        (row.gross || 0).toFixed(2),
        (row.salaryGross || 0).toFixed(2),
        (row.grossDiff || 0).toFixed(2),
        (row.pf || 0).toFixed(2),
        (row.salaryPF || 0).toFixed(2),
        (row.pfDiff || 0).toFixed(2),
      ]),
      [
        "Total",
        "",
        "",
        totals.ccGross.toFixed(2),
        totals.salaryGross.toFixed(2),
        ((totals.ccGross || 0) - (totals.salaryGross || 0)).toFixed(2),
        totals.ccPF.toFixed(2),
        totals.salaryPF.toFixed(2),
        ((totals.ccPF || 0) - (totals.salaryPF || 0)).toFixed(2),
      ],
    ];
    const ws2 = XLSX.utils.aoa_to_sheet(differencesData);
    XLSX.utils.book_append_sheet(wb, ws2, "Differences");

    XLSX.writeFile(wb, `Tally_Report_${month}.xlsx`);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Container sx={{ padding: 4 }}>
        <Card sx={{ mb: 4, overflow: 'visible' }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" component="h1" gutterBottom sx={{ mb: 0 }}>
                Tally Output
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title="Configure Cost Center Mappings">
                  <IconButton
                    onClick={() => setMappingModalOpen(true)}
                    color="primary"
                    sx={{ mr: 1 }}
                  >
                    <SettingsIcon />
                  </IconButton>
                </Tooltip>
                <Button variant="contained" onClick={() => { setMonth(""); }}>
                  Refresh Data
                </Button>
              </Box>
            </Box>

            <Box sx={{ mb: 3, width: "100%" }}>
              <FormControl fullWidth>
                <InputLabel>Month</InputLabel>
                <Select
                  value={month}
                  onChange={(e) => setMonth(e.target.value)}
                  label="Month"
                >
                  {uniqueMonths.map((m) => (
                    <MenuItem key={m} value={m}>
                      {m}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </CardContent>
        </Card>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Aggregated Data (Cost Center Totals)
                </Typography>
                {aggregatedData.length === 0 ? (
                  <Typography>No aggregated data available for this month.</Typography>
                ) : (
                  <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Sl No</TableCell>
                          <TableCell>Cost Center</TableCell>
                          <TableCell>Gross Salary</TableCell>
                          <TableCell>PF</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {aggregatedData.map((row, index) => (
                          <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                            <TableCell>{index + 1}</TableCell>
                            <TableCell>{row.costCenter || ""}</TableCell>
                            <TableCell>{(row.gross || 0).toFixed(2)}</TableCell>
                            <TableCell>{(row.pf || 0).toFixed(2)}</TableCell>
                          </TableRow>
                        ))}
                        <TableRow>
                          <TableCell colSpan={2} sx={{ fontWeight: "bold" }}>
                            Total
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {(totals.ccGross || 0).toFixed(2)}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {(totals.ccPF || 0).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </CardContent>
            </Card>

            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Salary vs Cost Center Comparison
                </Typography>
                <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Sl No</TableCell>
                        <TableCell>Cost Center</TableCell>
                        <TableCell>Cost Center Gross</TableCell>
                        <TableCell>Salary Gross</TableCell>
                        <TableCell>Gross Difference</TableCell>
                        <TableCell>Cost Center PF</TableCell>
                        <TableCell>Salary PF</TableCell>
                        <TableCell>PF Difference</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {differenceData.map((row, index) => (
                        <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{row.costCenter || ""}</TableCell>
                          <TableCell>{(row.gross || 0).toFixed(2)}</TableCell>
                          <TableCell>{(row.salaryGross || 0).toFixed(2)}</TableCell>
                          <TableCell sx={{ color: row.grossDiff !== 0 ? "red" : "inherit" }}>
                            {(row.grossDiff || 0).toFixed(2)}
                          </TableCell>
                          <TableCell>{(row.pf || 0).toFixed(2)}</TableCell>
                          <TableCell>{(row.salaryPF || 0).toFixed(2)}</TableCell>
                          <TableCell sx={{ color: row.pfDiff !== 0 ? "red" : "inherit" }}>
                            {(row.pfDiff || 0).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={2} sx={{ fontWeight: "bold" }}>
                          Total
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.ccGross || 0).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.salaryGross || 0).toFixed(2)}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: "bold",
                            color: (totals.ccGross - totals.salaryGross) !== 0 ? "red" : "inherit",
                          }}
                        >
                          {((totals.ccGross || 0) - (totals.salaryGross || 0)).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.ccPF || 0).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.salaryPF || 0).toFixed(2)}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: "bold",
                            color: (totals.ccPF - totals.salaryPF) !== 0 ? "red" : "inherit",
                          }}
                        >
                          {((totals.ccPF || 0) - (totals.salaryPF || 0)).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="contained"
                onClick={handleExport}
                sx={{ px: 3 }}
              >
                Export as Excel
              </Button>
            </Box>
          </>
        )}

        {/* Cost Center Mapping Modal */}
        <CostCenterMappingModal
          open={mappingModalOpen}
          onClose={() => setMappingModalOpen(false)}
          costCenters={uniqueCostCenters}
        />
      </Container>
    </motion.div>
  );
};

export default Tally;
